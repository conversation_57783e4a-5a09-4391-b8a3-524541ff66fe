<?php $__env->startSection('content'); ?>

    <link rel="stylesheet" href="https://cdn.datatables.net/2.1.8/css/dataTables.dataTables.min.css" />

    <style>
        .dt-input {
            margin-right: 10px;
        }
    </style>


    <div id="loading-overlay" style="display: none">
        <div class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>


    <div class="checkout">
        <div class="card shadow-none border">
            <div class="card-body p-4">
                <?php echo $__env->make('layouts.event_details', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php echo $__env->make('layouts.page_navigation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <div id="attendees_list">
                    <table class="table table-bordered table-hover" id="attendees_table" style="font-size: 12px;">
                        <thead class="">
                            <tr style="border-top: none">
                                <th>First Name</th>
                                <th>Last Name</th>
                                <th>Company</th>
                                <th>Email Address</th>
                                <th>Phone</th>
                                <th>Country</th>
                                <th>Job Title</th>
                                <th>Speciality</th>
                                <th>Hotel Information</th>
                                <th>Flight Information</th>
                                <th>Passport Information</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                                $hasValidUsers = false;
                                if ($users && $event->is_completed == 2) {
                                    foreach ($users as $item) {
                                        if ($item->status == 1) {
                                            $hasValidUsers = true;
                                            break;
                                        }
                                    }
                                }
                            ?>
                            <?php if($users && $event->is_completed == 2 && $hasValidUsers): ?>
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($item->status == 1): ?>
                                        <?php
                                            $attendeesInfo = \App\Models\AttendeesInfo::where('register_id', $item->id)->first();
                                        ?>
                                        <tr>
                                            <td><?php echo e($item->first_name); ?></td>
                                            <td><?php echo e($item->last_name); ?></td>
                                            <td><?php echo e($item->company); ?></td>
                                            <td><?php echo e($item->email); ?></td>
                                            <td><?php echo e($item->phone); ?></td>
                                            <td><?php echo e($item->country ?? 'N/A'); ?></td>
                                            <td><?php echo e($item->title ?? 'N/A'); ?></td>
                                            <td><?php echo e($item->job_title ?? 'N/A'); ?></td>
                                            <td>
                                                <span class="badge <?php echo e($attendeesInfo && $attendeesInfo->hotel_information ? 'bg-success' : 'bg-warning'); ?>"><small><?php echo e($attendeesInfo && $attendeesInfo->hotel_information ? 'Uploaded' : 'Not Uploaded'); ?></small></span>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo e($attendeesInfo && $attendeesInfo->flight_information ? 'bg-success' : 'bg-warning'); ?>"><small><?php echo e($attendeesInfo && $attendeesInfo->flight_information ? 'Uploaded' : 'Not Uploaded'); ?></small></span>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo e($attendeesInfo && $attendeesInfo->passport_details ? 'bg-success' : 'bg-warning'); ?>"><small><?php echo e($attendeesInfo && $attendeesInfo->passport_details ? 'Uploaded' : 'Not Uploaded'); ?></small></span>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-primary" onclick="addFile(this, <?php echo e($item->id); ?>)">
                                                    <?php if($attendeesInfo && ($attendeesInfo->flight_information || $attendeesInfo->hotel_information || $attendeesInfo->passport_details)): ?>
                                                        Manage Files
                                                    <?php else: ?>
                                                        Add File
                                                    <?php endif; ?>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="12" class="text-center">
                                        <?php if($event->is_completed != 2): ?>
                                            Oops, in order to see the attendees' info, please shortlist your registrants first.
                                        <?php else: ?>
                                            No shortlisted attendees found.
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>

                    <?php if($hasValidUsers): ?>
                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                new DataTable('#attendees_table');
                            });
                        </script>
                    <?php endif; ?>
                </div>


            </div>
        </div>
    </div>

    <!-- File Upload Modal -->
    <div class="modal fade" id="fileUploadModal" tabindex="-1" aria-labelledby="fileUploadModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="fileUploadModalLabel">Dr. John Doe</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6 class="mb-3" id="modalFileTitle">Add File</h6>

                    <form id="fileUploadForm" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" id="registerId" name="register_id" value="">

                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <label class="form-label mb-0">Flight Information</label>
                                <div class="d-flex gap-2">
                                    <input type="file" id="flightFile" name="flight_file" class="d-none" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                    <button type="button" id="flightAddBtn" class="btn btn-sm" style="background-color: #ff6b35; color: white;" onclick="document.getElementById('flightFile').click()">
                                        <i class="ti ti-upload"></i> <span id="flightBtnText">Add File</span>
                                    </button>
                                    <button type="button" id="flightDownloadBtn" class="btn btn-sm btn-outline-primary d-none" onclick="downloadFile('flight')">
                                        <i class="ti ti-download"></i>
                                    </button>
                                    <button type="button" id="flightDeleteBtn" class="btn btn-sm btn-outline-danger d-none" onclick="deleteFile('flight')">
                                        <i class="ti ti-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="text-end mt-1">
                                <span id="flightFileName" class="text-muted small"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <label class="form-label mb-0">Hotel Information</label>
                                <div class="d-flex gap-2">
                                    <input type="file" id="hotelFile" name="hotel_file" class="d-none" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                    <button type="button" id="hotelAddBtn" class="btn btn-sm" style="background-color: #ff6b35; color: white;" onclick="document.getElementById('hotelFile').click()">
                                        <i class="ti ti-upload"></i> <span id="hotelBtnText">Add File</span>
                                    </button>
                                    <button type="button" id="hotelDownloadBtn" class="btn btn-sm btn-outline-primary d-none" onclick="downloadFile('hotel')">
                                        <i class="ti ti-download"></i>
                                    </button>
                                    <button type="button" id="hotelDeleteBtn" class="btn btn-sm btn-outline-danger d-none" onclick="deleteFile('hotel')">
                                        <i class="ti ti-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="text-end mt-1">
                                <span id="hotelFileName" class="text-muted small"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <label class="form-label mb-0">Passport Detail</label>
                                <div class="d-flex gap-2">
                                    <input type="file" id="passportFile" name="passport_file" class="d-none" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                    <button type="button" id="passportAddBtn" class="btn btn-sm" style="background-color: #ff6b35; color: white;" onclick="document.getElementById('passportFile').click()">
                                        <i class="ti ti-upload"></i> <span id="passportBtnText">Add File</span>
                                    </button>
                                    <button type="button" id="passportDownloadBtn" class="btn btn-sm btn-outline-primary d-none" onclick="downloadFile('passport')">
                                        <i class="ti ti-download"></i>
                                    </button>
                                    <button type="button" id="passportDeleteBtn" class="btn btn-sm btn-outline-danger d-none" onclick="deleteFile('passport')">
                                        <i class="ti ti-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="text-end mt-1">
                                <span id="passportFileName" class="text-muted small"></span>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="saveFiles()">Save</button>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('css'); ?>
    <style>
        .qr-code-link {
            position: relative;
            display: inline-block;
        }

        .qr-code-container {
            position: relative;
            display: inline-block;
        }

        .qr-hover-icon {
            position: absolute;
            font-weight: 800;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 48px;
            color: #000000;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .qr-code-link:hover .qr-hover-icon {
            opacity: 1;
        }

        .qr-code-link:hover img {
            opacity: 0.4;
        }

        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.5);
            z-index: 9998;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
            border-width: 0.3rem;
        }

        /* Daha önce seçili olan checkbox'lar için stil */
        .previously-selected {
            opacity: 0.7;
        }

        .previously-selected:disabled {
            cursor: not-allowed;
        }

        /* Daha önce seçili olan satırlar için arka plan rengi */
        tr:has(.previously-selected) {
            background-color: #f8f9fa;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('js'); ?>
    <script src="https://cdn.datatables.net/2.1.8/js/dataTables.min.js"></script>

    <script>
        function addFile(button, registerId) {
            // Get user info from the table row
            const row = button.closest('tr');
            const firstName = row.cells[0].textContent;
            const lastName = row.cells[1].textContent;
            const email = row.cells[3].textContent;

            // Set modal title
            document.getElementById('fileUploadModalLabel').textContent = `${firstName} ${lastName}`;

            // Set register ID
            document.getElementById('registerId').value = registerId;

            // Clear previous file selections
            clearFileSelections();

            // Load existing files for this register
            loadExistingFiles(registerId);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('fileUploadModal'));
            modal.show();
        }

        function loadExistingFiles(registerId) {
            // Fetch existing files from server
            fetch(`/admin/attendees-info/get-files/${registerId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.attendees_info) {
                        const info = data.attendees_info;
                        let hasAnyFile = false;

                        // Flight Information
                        if (info.flight_information) {
                            document.getElementById('flightFileName').textContent = info.flight_information;
                            document.getElementById('flightDownloadBtn').classList.remove('d-none');
                            document.getElementById('flightDeleteBtn').classList.remove('d-none');
                            document.getElementById('flightBtnText').textContent = 'Update File';
                            hasAnyFile = true;
                        } else {
                            document.getElementById('flightDownloadBtn').classList.add('d-none');
                            document.getElementById('flightDeleteBtn').classList.add('d-none');
                            document.getElementById('flightBtnText').textContent = 'Add File';
                        }

                        // Hotel Information
                        if (info.hotel_information) {
                            document.getElementById('hotelFileName').textContent = info.hotel_information;
                            document.getElementById('hotelDownloadBtn').classList.remove('d-none');
                            document.getElementById('hotelDeleteBtn').classList.remove('d-none');
                            document.getElementById('hotelBtnText').textContent = 'Update File';
                            hasAnyFile = true;
                        } else {
                            document.getElementById('hotelDownloadBtn').classList.add('d-none');
                            document.getElementById('hotelDeleteBtn').classList.add('d-none');
                            document.getElementById('hotelBtnText').textContent = 'Add File';
                        }

                        // Passport Details
                        if (info.passport_details) {
                            document.getElementById('passportFileName').textContent = info.passport_details;
                            document.getElementById('passportDownloadBtn').classList.remove('d-none');
                            document.getElementById('passportDeleteBtn').classList.remove('d-none');
                            document.getElementById('passportBtnText').textContent = 'Update File';
                            hasAnyFile = true;
                        } else {
                            document.getElementById('passportDownloadBtn').classList.add('d-none');
                            document.getElementById('passportDeleteBtn').classList.add('d-none');
                            document.getElementById('passportBtnText').textContent = 'Add File';
                        }

                        // Update modal title
                        document.getElementById('modalFileTitle').textContent = hasAnyFile ? 'Update File' : 'Add File';
                    }
                })
                .catch(error => {
                    console.error('Error loading existing files:', error);
                });
        }

        function clearFileSelections() {
            document.getElementById('flightFile').value = '';
            document.getElementById('hotelFile').value = '';
            document.getElementById('passportFile').value = '';
            document.getElementById('flightFileName').textContent = '';
            document.getElementById('hotelFileName').textContent = '';
            document.getElementById('passportFileName').textContent = '';

            // Hide download and delete buttons and reset button texts
            document.getElementById('flightDownloadBtn').classList.add('d-none');
            document.getElementById('hotelDownloadBtn').classList.add('d-none');
            document.getElementById('passportDownloadBtn').classList.add('d-none');
            document.getElementById('flightDeleteBtn').classList.add('d-none');
            document.getElementById('hotelDeleteBtn').classList.add('d-none');
            document.getElementById('passportDeleteBtn').classList.add('d-none');
            document.getElementById('flightBtnText').textContent = 'Add File';
            document.getElementById('hotelBtnText').textContent = 'Add File';
            document.getElementById('passportBtnText').textContent = 'Add File';
            document.getElementById('modalFileTitle').textContent = 'Add File';
        }

        function downloadFile(type) {
            let fileName = '';

            switch(type) {
                case 'flight':
                    fileName = document.getElementById('flightFileName').textContent;
                    break;
                case 'hotel':
                    fileName = document.getElementById('hotelFileName').textContent;
                    break;
                case 'passport':
                    fileName = document.getElementById('passportFileName').textContent;
                    break;
            }

            if (fileName) {
                const storagePath = '<?php echo e(env("STORAGE_PATH")); ?>';
                const url = `${storagePath}/attendees_files/${fileName}`;
                Object.assign(document.createElement('a'), { href: url, download: fileName }).click();

            }
        }

        function deleteFile(type) {
            if (!confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
                return;
            }

            const registerId = document.getElementById('registerId').value;

            fetch(`/admin/attendees-info/delete-file`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    register_id: registerId,
                    file_type: type
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('File deleted successfully!');

                    // Update UI based on file type
                    switch(type) {
                        case 'flight':
                            document.getElementById('flightFileName').textContent = '';
                            document.getElementById('flightDownloadBtn').classList.add('d-none');
                            document.getElementById('flightDeleteBtn').classList.add('d-none');
                            document.getElementById('flightBtnText').textContent = 'Add File';
                            break;
                        case 'hotel':
                            document.getElementById('hotelFileName').textContent = '';
                            document.getElementById('hotelDownloadBtn').classList.add('d-none');
                            document.getElementById('hotelDeleteBtn').classList.add('d-none');
                            document.getElementById('hotelBtnText').textContent = 'Add File';
                            break;
                        case 'passport':
                            document.getElementById('passportFileName').textContent = '';
                            document.getElementById('passportDownloadBtn').classList.add('d-none');
                            document.getElementById('passportDeleteBtn').classList.add('d-none');
                            document.getElementById('passportBtnText').textContent = 'Add File';
                            break;
                    }

                    // Refresh page to update table status
                    location.reload();
                } else {
                    alert('Delete failed: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Delete failed!');
            });
        }

        // File selection handlers
        document.getElementById('flightFile').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : '';
            document.getElementById('flightFileName').textContent = fileName;
            if (fileName) {
                document.getElementById('flightBtnText').textContent = 'Update File';
            }
        });

        document.getElementById('hotelFile').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : '';
            document.getElementById('hotelFileName').textContent = fileName;
            if (fileName) {
                document.getElementById('hotelBtnText').textContent = 'Update File';
            }
        });

        document.getElementById('passportFile').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : '';
            document.getElementById('passportFileName').textContent = fileName;
            if (fileName) {
                document.getElementById('passportBtnText').textContent = 'Update File';
            }
        });

        function saveFiles() {
            const formData = new FormData();
            const registerId = document.getElementById('registerId').value;

            // Add files to form data
            const flightFile = document.getElementById('flightFile').files[0];
            const hotelFile = document.getElementById('hotelFile').files[0];
            const passportFile = document.getElementById('passportFile').files[0];

            if (flightFile) formData.append('flight_file', flightFile);
            if (hotelFile) formData.append('hotel_file', hotelFile);
            if (passportFile) formData.append('passport_file', passportFile);

            formData.append('register_id', registerId);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            // Send to controller
            fetch(`/admin/attendees-info/upload-files`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Files uploaded successfully!');
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('fileUploadModal'));
                    modal.hide();
                    // Refresh page or update table
                    location.reload();
                } else {
                    alert('Upload failed: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Upload failed!');
            });
        }

        // Example function for uploading flight info (to be implemented)
        function uploadFlightInfo(registerId, file) {
            const formData = new FormData();
            formData.append('flight_info', file);
            formData.append('register_id', registerId);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            fetch(`/admin/event/<?php echo e($event->id); ?>/upload-flight-info`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('File uploaded successfully!');
                } else {
                    alert('Upload failed: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Upload failed!');
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\bd-qr-badge\resources\views/event/event_detail/attendees_info.blade.php ENDPATH**/ ?>